const CHUNK_PUBLIC_PATH = "server/app/base64/route.js";
const runtime = require("../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/07552_@supabase_node-fetch_lib_index_bb3af036.js");
runtime.loadChunk("server/chunks/f07ee_next_c19710a3._.js");
runtime.loadChunk("server/chunks/78375_tailwind-merge_dist_bundle-mjs_mjs_9a710939._.js");
runtime.loadChunk("server/chunks/dc6dc_tr46_c974d224._.js");
runtime.loadChunk("server/chunks/e51d9_ws_42528b4b._.js");
runtime.loadChunk("server/chunks/4294a_@supabase_auth-js_dist_module_8a467908._.js");
runtime.loadChunk("server/chunks/618d5_zod_dist_esm_7824c5d1._.js");
runtime.loadChunk("server/chunks/node_modules__pnpm_1516d1c2._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__8f4cfa79._.js");
runtime.getOrInstantiateRuntimeModule("[project]/packages/api/.next-internal/server/app/base64/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/packages/api/app/base64/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/packages/api/app/base64/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
