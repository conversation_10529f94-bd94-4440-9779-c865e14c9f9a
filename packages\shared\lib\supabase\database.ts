export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  app_access: {
    Tables: {
      capability: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          name: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: never
          name: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: never
          name?: string
        }
        Relationships: []
      }
      role: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          name: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: never
          name: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: never
          name?: string
        }
        Relationships: []
      }
      role_capability: {
        Row: {
          capability_id: number
          created_at: string | null
          role_id: number
        }
        Insert: {
          capability_id: number
          created_at?: string | null
          role_id: number
        }
        Update: {
          capability_id?: number
          created_at?: string | null
          role_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "role_capability_capability_id_fkey"
            columns: ["capability_id"]
            isOneToOne: false
            referencedRelation: "capability"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_capability_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "role"
            referencedColumns: ["id"]
          },
        ]
      }
      user_role: {
        Row: {
          created_at: string | null
          role_id: number
          user_id: string
        }
        Insert: {
          created_at?: string | null
          role_id: number
          user_id: string
        }
        Update: {
          created_at?: string | null
          role_id?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_role_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "role"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      assign_role_to_user: {
        Args: { v_role_name: string; v_user_id: string }
        Returns: boolean
      }
      define_role_capability: {
        Args: { v_role_name: string; v_capability_names: string[] }
        Returns: boolean
      }
      has_capability: {
        Args: { capability_name: string; p_user_id?: string }
        Returns: boolean
      }
      has_role: {
        Args: { role_name: string }
        Returns: boolean
      }
      revoke_role_from_user: {
        Args: { v_user_id: string; v_role_name: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_account: {
    Tables: {
      banned_user: {
        Row: {
          banned_at: string
          banned_by: string | null
          previous_roles: string[]
          reason: string | null
          user_id: string
        }
        Insert: {
          banned_at?: string
          banned_by?: string | null
          previous_roles?: string[]
          reason?: string | null
          user_id: string
        }
        Update: {
          banned_at?: string
          banned_by?: string | null
          previous_roles?: string[]
          reason?: string | null
          user_id?: string
        }
        Relationships: []
      }
      favorite_activity: {
        Row: {
          activity_id: string
          created_at: string | null
          user_id: string
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          user_id: string
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      iban: {
        Row: {
          created_at: string | null
          iban: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          iban: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          iban?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      kyc: {
        Row: {
          created_at: string | null
          full_name: string | null
          id: string
          status: Database["app_account"]["Enums"]["kyc_status"]
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          full_name?: string | null
          id?: string
          status?: Database["app_account"]["Enums"]["kyc_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          full_name?: string | null
          id?: string
          status?: Database["app_account"]["Enums"]["kyc_status"]
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      locale: {
        Row: {
          created_at: string | null
          locale: "en" | "ko" | "ja" | "tr"
          user_id: string
        }
        Insert: {
          created_at?: string | null
          locale: "en" | "ko" | "ja" | "tr"
          user_id: string
        }
        Update: {
          created_at?: string | null
          locale?: "en" | "ko" | "ja" | "tr"
          user_id?: string
        }
        Relationships: []
      }
      notification: {
        Row: {
          action_url: string | null
          created_at: string | null
          data: Json
          id: string
          message_key: string
          read_at: string | null
          recipient_id: string
          title_key: string
          type: string
        }
        Insert: {
          action_url?: string | null
          created_at?: string | null
          data: Json
          id?: string
          message_key: string
          read_at?: string | null
          recipient_id: string
          title_key: string
          type: string
        }
        Update: {
          action_url?: string | null
          created_at?: string | null
          data?: Json
          id?: string
          message_key?: string
          read_at?: string | null
          recipient_id?: string
          title_key?: string
          type?: string
        }
        Relationships: []
      }
      privacy: {
        Row: {
          show_activity: boolean
          show_in_leaderboard: boolean
          user_id: string
        }
        Insert: {
          show_activity?: boolean
          show_in_leaderboard?: boolean
          user_id: string
        }
        Update: {
          show_activity?: boolean
          show_in_leaderboard?: boolean
          user_id?: string
        }
        Relationships: []
      }
      profile: {
        Row: {
          bio: string | null
          birth_date: string | null
          gender: "male" | "female" | null
          join_date: string | null
          nickname: string | null
          user_id: string
          username: string
        }
        Insert: {
          bio?: string | null
          birth_date?: string | null
          gender?: "male" | "female" | null
          join_date?: string | null
          nickname?: string | null
          user_id: string
          username: string
        }
        Update: {
          bio?: string | null
          birth_date?: string | null
          gender?: "male" | "female" | null
          join_date?: string | null
          nickname?: string | null
          user_id?: string
          username?: string
        }
        Relationships: []
      }
      social_link: {
        Row: {
          created_at: string | null
          id: string
          link: string
          platform: number
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          link: string
          platform: number
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          link?: string
          platform?: number
          user_id?: string
        }
        Relationships: []
      }
      user_block: {
        Row: {
          blocked_id: string
          blocker_id: string
          created_at: string
        }
        Insert: {
          blocked_id: string
          blocker_id?: string
          created_at?: string
        }
        Update: {
          blocked_id?: string
          blocker_id?: string
          created_at?: string
        }
        Relationships: []
      }
      visit: {
        Row: {
          visited_at: string | null
          visited_id: string
          visitor_id: string
        }
        Insert: {
          visited_at?: string | null
          visited_id: string
          visitor_id: string
        }
        Update: {
          visited_at?: string | null
          visited_id?: string
          visitor_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_notification: {
        Args: {
          p_type: string
          p_recipient_id: string
          p_title_key: string
          p_message_key: string
          p_data: Json
          p_action_url?: string
        }
        Returns: {
          action_url: string | null
          created_at: string | null
          data: Json
          id: string
          message_key: string
          read_at: string | null
          recipient_id: string
          title_key: string
          type: string
        }
      }
      read_notification: {
        Args: { p_notification_id: string }
        Returns: undefined
      }
      record_profile_visit: {
        Args: { p_visited_id: string }
        Returns: undefined
      }
    }
    Enums: {
      kyc_status: "draft" | "pending" | "approved" | "rejected"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_catalog: {
    Tables: {
      activity: {
        Row: {
          category_id: string | null
          color: Json | null
          created_at: string | null
          description: Json | null
          id: string
          name: Json
          service_count: number
          slug: string | null
        }
        Insert: {
          category_id?: string | null
          color?: Json | null
          created_at?: string | null
          description?: Json | null
          id?: string
          name: Json
          service_count?: number
          slug?: string | null
        }
        Update: {
          category_id?: string | null
          color?: Json | null
          created_at?: string | null
          description?: Json | null
          id?: string
          name?: Json
          service_count?: number
          slug?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activity_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_field: {
        Row: {
          activity_id: string
          field_id: string
        }
        Insert: {
          activity_id: string
          field_id: string
        }
        Update: {
          activity_id?: string
          field_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_field_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_field_field_id_fkey"
            columns: ["field_id"]
            isOneToOne: false
            referencedRelation: "field"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_field_option: {
        Row: {
          activity_id: string
          field_option_id: string
        }
        Insert: {
          activity_id: string
          field_option_id: string
        }
        Update: {
          activity_id?: string
          field_option_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_field_option_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_field_option_field_option_id_fkey"
            columns: ["field_option_id"]
            isOneToOne: false
            referencedRelation: "field_option"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_pricing: {
        Row: {
          activity_id: string
          pricing_id: string
        }
        Insert: {
          activity_id: string
          pricing_id: string
        }
        Update: {
          activity_id?: string
          pricing_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_pricing_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_pricing_pricing_id_fkey"
            columns: ["pricing_id"]
            isOneToOne: false
            referencedRelation: "pricing"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_service: {
        Row: {
          activity_id: string
          service_id: string
        }
        Insert: {
          activity_id: string
          service_id: string
        }
        Update: {
          activity_id?: string
          service_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_service_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_service_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_tag: {
        Row: {
          activity_id: string
          created_at: string | null
          tag_id: string
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          tag_id: string
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_tag_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_tag_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tag"
            referencedColumns: ["id"]
          },
        ]
      }
      category: {
        Row: {
          cover: string | null
          created_at: string | null
          description: Json | null
          icon: string | null
          id: string
          name: Json
          parent_category_id: string | null
          slug: string | null
        }
        Insert: {
          cover?: string | null
          created_at?: string | null
          description?: Json | null
          icon?: string | null
          id?: string
          name: Json
          parent_category_id?: string | null
          slug?: string | null
        }
        Update: {
          cover?: string | null
          created_at?: string | null
          description?: Json | null
          icon?: string | null
          id?: string
          name?: Json
          parent_category_id?: string | null
          slug?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "category_parent_category_id_fkey"
            columns: ["parent_category_id"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
        ]
      }
      category_field: {
        Row: {
          category_id: string
          field_id: string
        }
        Insert: {
          category_id: string
          field_id: string
        }
        Update: {
          category_id?: string
          field_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "category_field_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "category_field_field_id_fkey"
            columns: ["field_id"]
            isOneToOne: false
            referencedRelation: "field"
            referencedColumns: ["id"]
          },
        ]
      }
      category_field_option: {
        Row: {
          category_id: string
          field_option_id: string
        }
        Insert: {
          category_id: string
          field_option_id: string
        }
        Update: {
          category_id?: string
          field_option_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "category_field_option_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "category_field_option_field_option_id_fkey"
            columns: ["field_option_id"]
            isOneToOne: false
            referencedRelation: "field_option"
            referencedColumns: ["id"]
          },
        ]
      }
      category_pricing: {
        Row: {
          category_id: string
          pricing_id: string
        }
        Insert: {
          category_id: string
          pricing_id: string
        }
        Update: {
          category_id?: string
          pricing_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "category_pricing_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "category_pricing_pricing_id_fkey"
            columns: ["pricing_id"]
            isOneToOne: false
            referencedRelation: "pricing"
            referencedColumns: ["id"]
          },
        ]
      }
      category_service: {
        Row: {
          category_id: string
          service_id: string
        }
        Insert: {
          category_id: string
          service_id: string
        }
        Update: {
          category_id?: string
          service_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "category_service_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "category"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "category_service_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      field: {
        Row: {
          created_at: string | null
          description: Json | null
          id: string
          name: Json
          type: Database["app_catalog"]["Enums"]["field_type"]
        }
        Insert: {
          created_at?: string | null
          description?: Json | null
          id?: string
          name: Json
          type?: Database["app_catalog"]["Enums"]["field_type"]
        }
        Update: {
          created_at?: string | null
          description?: Json | null
          id?: string
          name?: Json
          type?: Database["app_catalog"]["Enums"]["field_type"]
        }
        Relationships: []
      }
      field_option: {
        Row: {
          created_at: string | null
          description: Json | null
          field_id: string | null
          id: string
          name: Json
        }
        Insert: {
          created_at?: string | null
          description?: Json | null
          field_id?: string | null
          id?: string
          name: Json
        }
        Update: {
          created_at?: string | null
          description?: Json | null
          field_id?: string | null
          id?: string
          name?: Json
        }
        Relationships: [
          {
            foreignKeyName: "field_option_field_id_fkey"
            columns: ["field_id"]
            isOneToOne: false
            referencedRelation: "field"
            referencedColumns: ["id"]
          },
        ]
      }
      platform: {
        Row: {
          created_at: string | null
          id: number
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      pricing: {
        Row: {
          created_at: string | null
          description: Json | null
          icon: string | null
          id: string
          name: Json
        }
        Insert: {
          created_at?: string | null
          description?: Json | null
          icon?: string | null
          id?: string
          name: Json
        }
        Update: {
          created_at?: string | null
          description?: Json | null
          icon?: string | null
          id?: string
          name?: Json
        }
        Relationships: []
      }
      service: {
        Row: {
          created_at: string | null
          id: string
          name: Json
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: Json
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: Json
        }
        Relationships: []
      }
      tag: {
        Row: {
          created_at: string | null
          description: Json | null
          id: string
          name: Json
          slug: string | null
        }
        Insert: {
          created_at?: string | null
          description?: Json | null
          id?: string
          name: Json
          slug?: string | null
        }
        Update: {
          created_at?: string | null
          description?: Json | null
          id?: string
          name?: Json
          slug?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      field_type: "text" | "checkbox" | "select" | "multiselect"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_chat: {
    Tables: {
      conversation: {
        Row: {
          created_at: string | null
          id: string
          is_blocked: boolean
          is_group: boolean
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_blocked?: boolean
          is_group?: boolean
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_blocked?: boolean
          is_group?: boolean
          updated_at?: string | null
        }
        Relationships: []
      }
      conversation_preference: {
        Row: {
          conversation_id: string
          created_at: string | null
          muted: boolean
          muted_until: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          conversation_id: string
          created_at?: string | null
          muted?: boolean
          muted_until?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string
          created_at?: string | null
          muted?: boolean
          muted_until?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_preference_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversation"
            referencedColumns: ["id"]
          },
        ]
      }
      deleted_message: {
        Row: {
          content: Json
          conversation_id: string
          created_at: string | null
          deleted_at: string | null
          id: string
          sender_id: string
          updated_at: string | null
        }
        Insert: {
          content: Json
          conversation_id: string
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          sender_id: string
          updated_at?: string | null
        }
        Update: {
          content?: Json
          conversation_id?: string
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          sender_id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      log: {
        Row: {
          action: string
          conversation_id: string
          created_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          action: string
          conversation_id: string
          created_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          action?: string
          conversation_id?: string
          created_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "log_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversation"
            referencedColumns: ["id"]
          },
        ]
      }
      member: {
        Row: {
          conversation_id: string
          created_at: string | null
          user_id: string
        }
        Insert: {
          conversation_id: string
          created_at?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string
          created_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "member_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversation"
            referencedColumns: ["id"]
          },
        ]
      }
      message: {
        Row: {
          content: Json
          conversation_id: string
          created_at: string | null
          deleted_at: string | null
          id: string
          sender_id: string
          updated_at: string | null
        }
        Insert: {
          content: Json
          conversation_id: string
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          sender_id: string
          updated_at?: string | null
        }
        Update: {
          content?: Json
          conversation_id?: string
          created_at?: string | null
          deleted_at?: string | null
          id?: string
          sender_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "message_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversation"
            referencedColumns: ["id"]
          },
        ]
      }
      message_read: {
        Row: {
          created_at: string | null
          message_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          message_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          message_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_read_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "message"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      is_conversation_blocked: {
        Args: { v_conversation_id: string }
        Returns: boolean
      }
      is_member: {
        Args: { v_conversation_id: string; v_user_id: string }
        Returns: boolean
      }
      start_conversation: {
        Args: { v_member_ids: string[] }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_dashboard: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      withdrawal: {
        Row: {
          commission: number | null
          currency: string | null
          id: string | null
          payment: number | null
          user_id: string | null
        }
        Insert: {
          commission?: number | null
          currency?: string | null
          id?: string | null
          payment?: number | null
          user_id?: string | null
        }
        Update: {
          commission?: number | null
          currency?: string | null
          id?: string | null
          payment?: number | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_media: {
    Tables: {
      image: {
        Row: {
          base64_placeholder: string | null
          bucket_id: string
          name: string
          object_id: string
        }
        Insert: {
          base64_placeholder?: string | null
          bucket_id: string
          name: string
          object_id: string
        }
        Update: {
          base64_placeholder?: string | null
          bucket_id?: string
          name?: string
          object_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_provider: {
    Tables: {
      activity: {
        Row: {
          activity_id: string
          created_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: []
      }
      activity_performance: {
        Row: {
          activity_id: string
          completed_orders: number
          rating: number
          reviews: number
        }
        Insert: {
          activity_id: string
          completed_orders?: number
          rating?: number
          reviews?: number
        }
        Update: {
          activity_id?: string
          completed_orders?: number
          rating?: number
          reviews?: number
        }
        Relationships: [
          {
            foreignKeyName: "activity_performance_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: true
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
        ]
      }
      application: {
        Row: {
          application_status: Database["app_provider"]["Enums"]["application_status"]
          created_at: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          application_status?: Database["app_provider"]["Enums"]["application_status"]
          created_at?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          application_status?: Database["app_provider"]["Enums"]["application_status"]
          created_at?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      approved_review: {
        Row: {
          created_at: string | null
          review_id: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          review_id: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          review_id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "approved_review_review_id_fkey"
            columns: ["review_id"]
            isOneToOne: true
            referencedRelation: "review"
            referencedColumns: ["id"]
          },
        ]
      }
      approved_service: {
        Row: {
          created_at: string | null
          service_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          service_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          service_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "approved_service_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: true
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "approved_service_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "approved_user"
            referencedColumns: ["user_id"]
          },
        ]
      }
      approved_service_modifier: {
        Row: {
          created_at: string | null
          service_modifier_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          service_modifier_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          service_modifier_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "approved_service_modifier_service_modifier_id_fkey"
            columns: ["service_modifier_id"]
            isOneToOne: true
            referencedRelation: "service_modifier"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "approved_service_modifier_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "approved_user"
            referencedColumns: ["user_id"]
          },
        ]
      }
      approved_user: {
        Row: {
          created_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      availability: {
        Row: {
          availability_type: Database["app_provider"]["Enums"]["availability_type"]
          day_of_week:
            | "monday"
            | "tuesday"
            | "wednesday"
            | "thursday"
            | "friday"
            | "saturday"
            | "sunday"
            | null
          end_time: string
          id: string
          start_time: string
          user_id: string
        }
        Insert: {
          availability_type: Database["app_provider"]["Enums"]["availability_type"]
          day_of_week?:
            | "monday"
            | "tuesday"
            | "wednesday"
            | "thursday"
            | "friday"
            | "saturday"
            | "sunday"
            | null
          end_time: string
          id?: string
          start_time: string
          user_id: string
        }
        Update: {
          availability_type?: Database["app_provider"]["Enums"]["availability_type"]
          day_of_week?:
            | "monday"
            | "tuesday"
            | "wednesday"
            | "thursday"
            | "friday"
            | "saturday"
            | "sunday"
            | null
          end_time?: string
          id?: string
          start_time?: string
          user_id?: string
        }
        Relationships: []
      }
      config: {
        Row: {
          cap_cost_for_question_submission: number
          cap_reward_for_question_answer: number
          cap_reward_to_customer_for_completed_order: number
          cap_reward_to_customer_for_review_submission: number
          cap_reward_to_provider_for_completed_order: number
          cap_reward_to_provider_for_review_approval: number
          id: boolean
        }
        Insert: {
          cap_cost_for_question_submission?: number
          cap_reward_for_question_answer?: number
          cap_reward_to_customer_for_completed_order?: number
          cap_reward_to_customer_for_review_submission?: number
          cap_reward_to_provider_for_completed_order?: number
          cap_reward_to_provider_for_review_approval?: number
          id?: boolean
        }
        Update: {
          cap_cost_for_question_submission?: number
          cap_reward_for_question_answer?: number
          cap_reward_to_customer_for_completed_order?: number
          cap_reward_to_customer_for_review_submission?: number
          cap_reward_to_provider_for_completed_order?: number
          cap_reward_to_provider_for_review_approval?: number
          id?: boolean
        }
        Relationships: []
      }
      field_value: {
        Row: {
          activity_id: string
          checkbox: boolean | null
          created_at: string | null
          field_id: string
          field_option_id: string | null
          id: string
          text: string | null
          user_id: string
        }
        Insert: {
          activity_id: string
          checkbox?: boolean | null
          created_at?: string | null
          field_id: string
          field_option_id?: string | null
          id?: string
          text?: string | null
          user_id: string
        }
        Update: {
          activity_id?: string
          checkbox?: boolean | null
          created_at?: string | null
          field_id?: string
          field_option_id?: string | null
          id?: string
          text?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "field_value_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
        ]
      }
      order: {
        Row: {
          completed_at: string | null
          created_at: string | null
          disputable_until: string | null
          escrow_id: string | null
          id: string
          next_status_for_receiver:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details: Json | null
          order_status: Database["app_provider"]["Enums"]["order_status"]
          receiver_id: string
          sender_id: string
          service_id: string
          soda_amount: number
          unit_count: number
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          disputable_until?: string | null
          escrow_id?: string | null
          id?: string
          next_status_for_receiver?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details?: Json | null
          order_status?: Database["app_provider"]["Enums"]["order_status"]
          receiver_id: string
          sender_id: string
          service_id: string
          soda_amount?: number
          unit_count?: number
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          disputable_until?: string | null
          escrow_id?: string | null
          id?: string
          next_status_for_receiver?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details?: Json | null
          order_status?: Database["app_provider"]["Enums"]["order_status"]
          receiver_id?: string
          sender_id?: string
          service_id?: string
          soda_amount?: number
          unit_count?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_receiver_id_fkey"
            columns: ["receiver_id"]
            isOneToOne: false
            referencedRelation: "approved_user"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "order_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "service"
            referencedColumns: ["id"]
          },
        ]
      }
      order_archive: {
        Row: {
          completed_at: string | null
          created_at: string | null
          disputable_until: string | null
          escrow_id: string | null
          id: string
          next_status_for_receiver:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details: Json | null
          order_status: Database["app_provider"]["Enums"]["order_status"] | null
          receiver_id: string | null
          sender_id: string | null
          service_id: string | null
          soda_amount: number | null
          unit_count: number | null
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          disputable_until?: string | null
          escrow_id?: string | null
          id: string
          next_status_for_receiver?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details?: Json | null
          order_status?:
            | Database["app_provider"]["Enums"]["order_status"]
            | null
          receiver_id?: string | null
          sender_id?: string | null
          service_id?: string | null
          soda_amount?: number | null
          unit_count?: number | null
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          disputable_until?: string | null
          escrow_id?: string | null
          id?: string
          next_status_for_receiver?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender?:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details?: Json | null
          order_status?:
            | Database["app_provider"]["Enums"]["order_status"]
            | null
          receiver_id?: string | null
          sender_id?: string | null
          service_id?: string | null
          soda_amount?: number | null
          unit_count?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      order_log: {
        Row: {
          changed_at: string | null
          changed_by: string | null
          id: string
          new_status: Database["app_provider"]["Enums"]["order_status"] | null
          old_status: Database["app_provider"]["Enums"]["order_status"] | null
          order_id: string
        }
        Insert: {
          changed_at?: string | null
          changed_by?: string | null
          id?: string
          new_status?: Database["app_provider"]["Enums"]["order_status"] | null
          old_status?: Database["app_provider"]["Enums"]["order_status"] | null
          order_id: string
        }
        Update: {
          changed_at?: string | null
          changed_by?: string | null
          id?: string
          new_status?: Database["app_provider"]["Enums"]["order_status"] | null
          old_status?: Database["app_provider"]["Enums"]["order_status"] | null
          order_id?: string
        }
        Relationships: []
      }
      performance: {
        Row: {
          answered_questions: number
          completed_orders: number
          earned_soda: number
          rating: number
          refunds: number
          refunds_by_intervention: number
          response_time: unknown | null
          reviews: number
          user_id: string
        }
        Insert: {
          answered_questions?: number
          completed_orders?: number
          earned_soda?: number
          rating?: number
          refunds?: number
          refunds_by_intervention?: number
          response_time?: unknown | null
          reviews?: number
          user_id: string
        }
        Update: {
          answered_questions?: number
          completed_orders?: number
          earned_soda?: number
          rating?: number
          refunds?: number
          refunds_by_intervention?: number
          response_time?: unknown | null
          reviews?: number
          user_id?: string
        }
        Relationships: []
      }
      profile: {
        Row: {
          bio: Json | null
          slug: string
          user_id: string
        }
        Insert: {
          bio?: Json | null
          slug: string
          user_id: string
        }
        Update: {
          bio?: Json | null
          slug?: string
          user_id?: string
        }
        Relationships: []
      }
      question: {
        Row: {
          asker_id: string
          cap_cost: number
          created_at: string | null
          id: string
          provider_id: string
          question_text: string
        }
        Insert: {
          asker_id: string
          cap_cost: number
          created_at?: string | null
          id?: string
          provider_id: string
          question_text: string
        }
        Update: {
          asker_id?: string
          cap_cost?: number
          created_at?: string | null
          id?: string
          provider_id?: string
          question_text?: string
        }
        Relationships: [
          {
            foreignKeyName: "question_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "approved_user"
            referencedColumns: ["user_id"]
          },
        ]
      }
      question_answer: {
        Row: {
          answer_text: string
          cap_reward: number
          created_at: string | null
          id: string
          provider_id: string
          question_id: string
        }
        Insert: {
          answer_text: string
          cap_reward: number
          created_at?: string | null
          id?: string
          provider_id: string
          question_id: string
        }
        Update: {
          answer_text?: string
          cap_reward?: number
          created_at?: string | null
          id?: string
          provider_id?: string
          question_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "question_answer_question_id_fkey"
            columns: ["question_id"]
            isOneToOne: true
            referencedRelation: "question"
            referencedColumns: ["id"]
          },
        ]
      }
      review: {
        Row: {
          comment: string | null
          comment_locale: "en" | "ko" | "ja" | "tr" | null
          created_at: string | null
          id: string
          rating: number
          user_id: string | null
        }
        Insert: {
          comment?: string | null
          comment_locale?: "en" | "ko" | "ja" | "tr" | null
          created_at?: string | null
          id: string
          rating: number
          user_id?: string | null
        }
        Update: {
          comment?: string | null
          comment_locale?: "en" | "ko" | "ja" | "tr" | null
          created_at?: string | null
          id?: string
          rating?: number
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "review_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "review_pass"
            referencedColumns: ["id"]
          },
        ]
      }
      review_pass: {
        Row: {
          activity_id: string
          created_at: string | null
          id: string
          order_details: Json
          order_id: string
          provider_id: string
          user_id: string | null
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          id?: string
          order_details: Json
          order_id: string
          provider_id: string
          user_id?: string | null
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          id?: string
          order_details?: Json
          order_id?: string
          provider_id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "review_pass_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "review_pass_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "approved_user"
            referencedColumns: ["user_id"]
          },
        ]
      }
      service: {
        Row: {
          activity_id: string | null
          created_at: string | null
          description: Json | null
          id: string
          max_unit_count: number
          name: Json | null
          pricing_id: string | null
          selected_service_id: string | null
          soda_amount: number
          status: "draft" | "published"
          updated_at: string | null
          user_id: string
        }
        Insert: {
          activity_id?: string | null
          created_at?: string | null
          description?: Json | null
          id?: string
          max_unit_count?: number
          name?: Json | null
          pricing_id?: string | null
          selected_service_id?: string | null
          soda_amount?: number
          status?: "draft" | "published"
          updated_at?: string | null
          user_id: string
        }
        Update: {
          activity_id?: string | null
          created_at?: string | null
          description?: Json | null
          id?: string
          max_unit_count?: number
          name?: Json | null
          pricing_id?: string | null
          selected_service_id?: string | null
          soda_amount?: number
          status?: "draft" | "published"
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
        ]
      }
      service_modifier: {
        Row: {
          activity_id: string
          created_at: string | null
          description: Json | null
          id: string
          name: Json
          soda_amount: number
          status: "draft" | "published"
          user_id: string
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          description?: Json | null
          id?: string
          name: Json
          soda_amount: number
          status?: "draft" | "published"
          user_id: string
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          description?: Json | null
          id?: string
          name?: Json
          soda_amount?: number
          status?: "draft" | "published"
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_modifier_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activity"
            referencedColumns: ["id"]
          },
        ]
      }
      status: {
        Row: {
          is_open_for_orders: boolean
          notes: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          is_open_for_orders?: boolean
          notes?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          is_open_for_orders?: boolean
          notes?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_favorite: {
        Row: {
          created_at: string | null
          provider_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          provider_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          provider_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_favorite_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "approved_user"
            referencedColumns: ["user_id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_disputable_window: {
        Args: { ts: string }
        Returns: string
      }
      gift_caps_to_provider: {
        Args: { p_cap_amount: unknown; p_provider_id: string }
        Returns: string
      }
      gift_soda_to_provider: {
        Args: { p_provider_id: string; p_soda_amount: unknown }
        Returns: string
      }
      handle_disputed_order: {
        Args: {
          p_order_id: string
          p_action: Database["app_provider"]["Enums"]["dispute_action"]
        }
        Returns: {
          completed_at: string | null
          created_at: string | null
          disputable_until: string | null
          escrow_id: string | null
          id: string
          next_status_for_receiver:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details: Json | null
          order_status: Database["app_provider"]["Enums"]["order_status"]
          receiver_id: string
          sender_id: string
          service_id: string
          soda_amount: number
          unit_count: number
          updated_at: string | null
        }
      }
      insert_order: {
        Args: {
          p_sender_id: string
          p_receiver_id: string
          p_service_id: string
          p_soda_amount: unknown
          p_unit_count: number
          p_order_details: Json
        }
        Returns: {
          completed_at: string | null
          created_at: string | null
          disputable_until: string | null
          escrow_id: string | null
          id: string
          next_status_for_receiver:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details: Json | null
          order_status: Database["app_provider"]["Enums"]["order_status"]
          receiver_id: string
          sender_id: string
          service_id: string
          soda_amount: number
          unit_count: number
          updated_at: string | null
        }
      }
      is_pending_or_approved: {
        Args: { p_user_id: string }
        Returns: boolean
      }
      is_review_approved: {
        Args: { p_review_id: string }
        Returns: boolean
      }
      is_service_approved: {
        Args: { p_service_id: string }
        Returns: boolean
      }
      is_service_modifier_approved: {
        Args: { p_service_modifier_id: string }
        Returns: boolean
      }
      is_user_approved: {
        Args: { p_user_id: string }
        Returns: boolean
      }
      release_completed_order_escrow: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      submit_order: {
        Args: {
          p_unit_count: number
          p_service_id: string
          p_service_modifier_ids?: string[]
        }
        Returns: {
          completed_at: string | null
          created_at: string | null
          disputable_until: string | null
          escrow_id: string | null
          id: string
          next_status_for_receiver:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details: Json | null
          order_status: Database["app_provider"]["Enums"]["order_status"]
          receiver_id: string
          sender_id: string
          service_id: string
          soda_amount: number
          unit_count: number
          updated_at: string | null
        }
      }
      submit_question: {
        Args: { p_provider_id: string; p_question_text: string }
        Returns: string
      }
      submit_question_answer: {
        Args: { p_question_id: string; p_answer_text: string }
        Returns: string
      }
      update_order_status: {
        Args: {
          p_order_id: string
          p_new_status: Database["app_provider"]["Enums"]["order_status"]
          p_dispute_description?: string
        }
        Returns: {
          completed_at: string | null
          created_at: string | null
          disputable_until: string | null
          escrow_id: string | null
          id: string
          next_status_for_receiver:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          next_status_for_sender:
            | Database["app_provider"]["Enums"]["order_status"][]
            | null
          order_details: Json | null
          order_status: Database["app_provider"]["Enums"]["order_status"]
          receiver_id: string
          sender_id: string
          service_id: string
          soda_amount: number
          unit_count: number
          updated_at: string | null
        }
      }
    }
    Enums: {
      application_status: "draft" | "submitted" | "approved" | "rejected"
      availability_type: "individual_day" | "weekdays" | "weekends"
      dispute_action: "refund" | "release"
      order_status:
        | "pending"
        | "accepted"
        | "rejected"
        | "completed"
        | "cancelled"
        | "in_dispute"
        | "refunded"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_support: {
    Tables: {
      flag: {
        Row: {
          additional_info: string | null
          created_at: string
          flagged_activity_id: string | null
          flagged_field_value_id: string | null
          flagged_id: string
          flagged_service_id: string | null
          flagged_service_modifier_id: string | null
          flagger_id: string
          id: string
          reason: string
        }
        Insert: {
          additional_info?: string | null
          created_at?: string
          flagged_activity_id?: string | null
          flagged_field_value_id?: string | null
          flagged_id: string
          flagged_service_id?: string | null
          flagged_service_modifier_id?: string | null
          flagger_id: string
          id?: string
          reason: string
        }
        Update: {
          additional_info?: string | null
          created_at?: string
          flagged_activity_id?: string | null
          flagged_field_value_id?: string | null
          flagged_id?: string
          flagged_service_id?: string | null
          flagged_service_modifier_id?: string | null
          flagger_id?: string
          id?: string
          reason?: string
        }
        Relationships: []
      }
      ticket: {
        Row: {
          assigned_to: string | null
          created_at: string
          disputed_order_id: string | null
          id: string
          problem_description: string
          resolution_notes: string | null
          status: Database["app_support"]["Enums"]["ticket_status"]
          title: string
          type: Database["app_support"]["Enums"]["ticket_type"]
          updated_at: string
          user_id: string | null
        }
        Insert: {
          assigned_to?: string | null
          created_at?: string
          disputed_order_id?: string | null
          id?: string
          problem_description: string
          resolution_notes?: string | null
          status?: Database["app_support"]["Enums"]["ticket_status"]
          title: string
          type: Database["app_support"]["Enums"]["ticket_type"]
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          assigned_to?: string | null
          created_at?: string
          disputed_order_id?: string | null
          id?: string
          problem_description?: string
          resolution_notes?: string | null
          status?: Database["app_support"]["Enums"]["ticket_status"]
          title?: string
          type?: Database["app_support"]["Enums"]["ticket_type"]
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      ticket_comment: {
        Row: {
          comment_message: string
          created_at: string
          id: string
          ticket_id: string
          user_id: string
        }
        Insert: {
          comment_message: string
          created_at?: string
          id?: string
          ticket_id: string
          user_id: string
        }
        Update: {
          comment_message?: string
          created_at?: string
          id?: string
          ticket_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ticket_comment_ticket_id_fkey"
            columns: ["ticket_id"]
            isOneToOne: false
            referencedRelation: "ticket"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      assign_ticket: {
        Args: { p_ticket_id: string; p_assigned_to?: string }
        Returns: {
          assigned_to: string | null
          created_at: string
          disputed_order_id: string | null
          id: string
          problem_description: string
          resolution_notes: string | null
          status: Database["app_support"]["Enums"]["ticket_status"]
          title: string
          type: Database["app_support"]["Enums"]["ticket_type"]
          updated_at: string
          user_id: string | null
        }
      }
      create_flag: {
        Args: {
          p_reason: string
          p_additional_info?: string
          p_flagged_service_modifier_id?: string
          p_flagged_service_id?: string
          p_flagged_activity_id?: string
          p_flagged_field_value_id?: string
          p_flagged_id: string
        }
        Returns: {
          additional_info: string | null
          created_at: string
          flagged_activity_id: string | null
          flagged_field_value_id: string | null
          flagged_id: string
          flagged_service_id: string | null
          flagged_service_modifier_id: string | null
          flagger_id: string
          id: string
          reason: string
        }
      }
      create_ticket: {
        Args: {
          p_problem_description: string
          p_disputed_order_id?: string
          p_title: string
          p_type: Database["app_support"]["Enums"]["ticket_type"]
        }
        Returns: {
          assigned_to: string | null
          created_at: string
          disputed_order_id: string | null
          id: string
          problem_description: string
          resolution_notes: string | null
          status: Database["app_support"]["Enums"]["ticket_status"]
          title: string
          type: Database["app_support"]["Enums"]["ticket_type"]
          updated_at: string
          user_id: string | null
        }
      }
      create_ticket_comment: {
        Args: { p_ticket_id: string; p_comment_message: string }
        Returns: {
          comment_message: string
          created_at: string
          id: string
          ticket_id: string
          user_id: string
        }
      }
      update_ticket_status: {
        Args: {
          p_ticket_id: string
          p_new_status: Database["app_support"]["Enums"]["ticket_status"]
          p_resolution_notes?: string
        }
        Returns: {
          assigned_to: string | null
          created_at: string
          disputed_order_id: string | null
          id: string
          problem_description: string
          resolution_notes: string | null
          status: Database["app_support"]["Enums"]["ticket_status"]
          title: string
          type: Database["app_support"]["Enums"]["ticket_type"]
          updated_at: string
          user_id: string | null
        }
      }
    }
    Enums: {
      ticket_status: "open" | "closed"
      ticket_type: "report" | "dispute"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_test: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      test_request: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_transaction: {
    Tables: {
      config: {
        Row: {
          base_currency: string | null
          commission_percent: number
          dispute_window_hours: number
          id: boolean
          in_progress_timeout_hours: number
          minimum_soda_withdrawal_amount: number
        }
        Insert: {
          base_currency?: string | null
          commission_percent?: number
          dispute_window_hours?: number
          id?: boolean
          in_progress_timeout_hours?: number
          minimum_soda_withdrawal_amount?: number
        }
        Update: {
          base_currency?: string | null
          commission_percent?: number
          dispute_window_hours?: number
          id?: boolean
          in_progress_timeout_hours?: number
          minimum_soda_withdrawal_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "config_base_currency_fkey"
            columns: ["base_currency"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["code"]
          },
        ]
      }
      currency: {
        Row: {
          code: string
          exchange_rate: number
          units_per_soda: number
        }
        Insert: {
          code: string
          exchange_rate?: number
          units_per_soda: number
        }
        Update: {
          code?: string
          exchange_rate?: number
          units_per_soda?: number
        }
        Relationships: []
      }
      deposit: {
        Row: {
          amount: number
          cap_credited: number | null
          created_at: string
          currency: string
          id: string
          soda_credited: number | null
          units_per_soda: number | null
          user_id: string
        }
        Insert: {
          amount: number
          cap_credited?: number | null
          created_at?: string
          currency?: string
          id?: string
          soda_credited?: number | null
          units_per_soda?: number | null
          user_id: string
        }
        Update: {
          amount?: number
          cap_credited?: number | null
          created_at?: string
          currency?: string
          id?: string
          soda_credited?: number | null
          units_per_soda?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "deposit_currency_fkey"
            columns: ["currency"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["code"]
          },
        ]
      }
      escrow: {
        Row: {
          id: string
          receiver_id: string | null
          sender_id: string | null
          soda_amount: number
          status: Database["app_transaction"]["Enums"]["escrow_status"]
        }
        Insert: {
          id?: string
          receiver_id?: string | null
          sender_id?: string | null
          soda_amount?: number
          status?: Database["app_transaction"]["Enums"]["escrow_status"]
        }
        Update: {
          id?: string
          receiver_id?: string | null
          sender_id?: string | null
          soda_amount?: number
          status?: Database["app_transaction"]["Enums"]["escrow_status"]
        }
        Relationships: []
      }
      transfer: {
        Row: {
          cap_amount: number | null
          created_at: string
          id: string
          receiver_id: string
          sender_id: string
          soda_amount: number | null
        }
        Insert: {
          cap_amount?: number | null
          created_at?: string
          id?: string
          receiver_id: string
          sender_id: string
          soda_amount?: number | null
        }
        Update: {
          cap_amount?: number | null
          created_at?: string
          id?: string
          receiver_id?: string
          sender_id?: string
          soda_amount?: number | null
        }
        Relationships: []
      }
      wallet: {
        Row: {
          cap_balance: number
          soda_balance: number
          user_id: string
        }
        Insert: {
          cap_balance?: number
          soda_balance?: number
          user_id: string
        }
        Update: {
          cap_balance?: number
          soda_balance?: number
          user_id?: string
        }
        Relationships: []
      }
      withdrawal: {
        Row: {
          currency: string
          currency_commission_amount: number | null
          currency_commission_percent: number | null
          currency_exchange_rate: number | null
          currency_paid_amount: number | null
          currency_total_amount: number | null
          currency_units_per_soda: number | null
          id: string
          processed_at: string | null
          requested_at: string | null
          soda_amount: number
          user_id: string
        }
        Insert: {
          currency?: string
          currency_commission_amount?: number | null
          currency_commission_percent?: number | null
          currency_exchange_rate?: number | null
          currency_paid_amount?: number | null
          currency_total_amount?: number | null
          currency_units_per_soda?: number | null
          id?: string
          processed_at?: string | null
          requested_at?: string | null
          soda_amount: number
          user_id: string
        }
        Update: {
          currency?: string
          currency_commission_amount?: number | null
          currency_commission_percent?: number | null
          currency_exchange_rate?: number | null
          currency_paid_amount?: number | null
          currency_total_amount?: number | null
          currency_units_per_soda?: number | null
          id?: string
          processed_at?: string | null
          requested_at?: string | null
          soda_amount?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "withdrawal_currency_fkey"
            columns: ["currency"]
            isOneToOne: false
            referencedRelation: "currency"
            referencedColumns: ["code"]
          },
        ]
      }
      withdrawal_request: {
        Row: {
          currency: string
          currency_commission_amount: number | null
          currency_commission_percent: number | null
          currency_exchange_rate: number | null
          currency_paid_amount: number | null
          currency_total_amount: number | null
          currency_units_per_soda: number | null
          processed_at: string | null
          requested_at: string | null
          soda_amount: number
          status: Database["app_transaction"]["Enums"]["withdrawal_request_status"]
          user_id: string
        }
        Insert: {
          currency?: string
          currency_commission_amount?: number | null
          currency_commission_percent?: number | null
          currency_exchange_rate?: number | null
          currency_paid_amount?: number | null
          currency_total_amount?: number | null
          currency_units_per_soda?: number | null
          processed_at?: string | null
          requested_at?: string | null
          soda_amount: number
          status?: Database["app_transaction"]["Enums"]["withdrawal_request_status"]
          user_id?: string
        }
        Update: {
          currency?: string
          currency_commission_amount?: number | null
          currency_commission_percent?: number | null
          currency_exchange_rate?: number | null
          currency_paid_amount?: number | null
          currency_total_amount?: number | null
          currency_units_per_soda?: number | null
          processed_at?: string | null
          requested_at?: string | null
          soda_amount?: number
          status?: Database["app_transaction"]["Enums"]["withdrawal_request_status"]
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      ensure_wallet_exists: {
        Args: { p_user_id: string }
        Returns: undefined
      }
    }
    Enums: {
      escrow_status: "pending" | "released" | "refunded"
      withdrawal_request_status: "pending" | "processing" | "completed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_webapp: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      caps_leaderboard: {
        Row: {
          cap_balance: number | null
          join_date: string | null
          nickname: string | null
          rank: number | null
          user_id: string | null
          username: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      get_user_caps_rank: {
        Args: { p_user_id?: string }
        Returns: {
          cap_balance: unknown
          rank: number
          total_users: number
        }[]
      }
      match_provider_for_user: {
        Args: { p_provider_ids?: string[] }
        Returns: string
      }
      refresh_caps_leaderboard: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  app_wiki: {
    Tables: {
      changelog: {
        Row: {
          content: Json
          created_at: string
          slug: string
          version: string
        }
        Insert: {
          content: Json
          created_at?: string
          slug: string
          version: string
        }
        Update: {
          content?: Json
          created_at?: string
          slug?: string
          version?: string
        }
        Relationships: []
      }
      document: {
        Row: {
          content: Json
          created_at: string
          description: Json
          slug: string
          title: Json
          updated_at: string
        }
        Insert: {
          content: Json
          created_at?: string
          description: Json
          slug: string
          title: Json
          updated_at?: string
        }
        Update: {
          content?: Json
          created_at?: string
          description?: Json
          slug?: string
          title?: Json
          updated_at?: string
        }
        Relationships: []
      }
      event: {
        Row: {
          content: Json
          created_at: string
          description: Json
          end_time: string
          slug: string
          start_time: string
          title: Json
          updated_at: string
        }
        Insert: {
          content: Json
          created_at?: string
          description: Json
          end_time: string
          slug: string
          start_time: string
          title: Json
          updated_at?: string
        }
        Update: {
          content?: Json
          created_at?: string
          description?: Json
          end_time?: string
          slug?: string
          start_time?: string
          title?: Json
          updated_at?: string
        }
        Relationships: []
      }
      news: {
        Row: {
          author_id: string | null
          content: Json
          created_at: string
          description: Json
          published_at: string
          slug: string
          title: Json
          updated_at: string
        }
        Insert: {
          author_id?: string | null
          content: Json
          created_at?: string
          description: Json
          published_at?: string
          slug: string
          title: Json
          updated_at?: string
        }
        Update: {
          author_id?: string | null
          content?: Json
          created_at?: string
          description?: Json
          published_at?: string
          slug?: string
          title?: Json
          updated_at?: string
        }
        Relationships: []
      }
      roadmap: {
        Row: {
          created_at: string | null
          description: string | null
          id: number
          priority: string | null
          status: Database["app_wiki"]["Enums"]["roadmap_status"]
          target_release_date: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: number
          priority?: string | null
          status?: Database["app_wiki"]["Enums"]["roadmap_status"]
          target_release_date?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: number
          priority?: string | null
          status?: Database["app_wiki"]["Enums"]["roadmap_status"]
          target_release_date?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      rule: {
        Row: {
          category: string | null
          content: Json
          created_at: string
          punishment: Json
          slug: string
          title: Json
          updated_at: string
        }
        Insert: {
          category?: string | null
          content: Json
          created_at?: string
          punishment: Json
          slug: string
          title: Json
          updated_at?: string
        }
        Update: {
          category?: string | null
          content?: Json
          created_at?: string
          punishment?: Json
          slug?: string
          title?: Json
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      roadmap_status:
        | "suggested"
        | "planned"
        | "in_progress"
        | "live"
        | "not_planned"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  app_access: {
    Enums: {},
  },
  app_account: {
    Enums: {
      kyc_status: ["draft", "pending", "approved", "rejected"],
    },
  },
  app_catalog: {
    Enums: {
      field_type: ["text", "checkbox", "select", "multiselect"],
    },
  },
  app_chat: {
    Enums: {},
  },
  app_dashboard: {
    Enums: {},
  },
  app_media: {
    Enums: {},
  },
  app_provider: {
    Enums: {
      application_status: ["draft", "submitted", "approved", "rejected"],
      availability_type: ["individual_day", "weekdays", "weekends"],
      dispute_action: ["refund", "release"],
      order_status: [
        "pending",
        "accepted",
        "rejected",
        "completed",
        "cancelled",
        "in_dispute",
        "refunded",
      ],
    },
  },
  app_support: {
    Enums: {
      ticket_status: ["open", "closed"],
      ticket_type: ["report", "dispute"],
    },
  },
  app_test: {
    Enums: {},
  },
  app_transaction: {
    Enums: {
      escrow_status: ["pending", "released", "refunded"],
      withdrawal_request_status: ["pending", "processing", "completed"],
    },
  },
  app_webapp: {
    Enums: {},
  },
  app_wiki: {
    Enums: {
      roadmap_status: [
        "suggested",
        "planned",
        "in_progress",
        "live",
        "not_planned",
      ],
    },
  },
  public: {
    Enums: {},
  },
} as const

